<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Price Tracker Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Price Tracker Component Test</h1>
        
        <!-- Container for the Price Tracker component -->
        <div id="price-tracker-container" class="bg-white rounded-lg shadow-lg p-6">
            <!-- Component will be rendered here -->
        </div>
    </div>

    <!-- Mock Chrome extension API for testing -->
    <script>
        // Mock chrome.storage.local for testing
        window.chrome = {
            storage: {
                local: {
                    get: (keys, callback) => {
                        // Mock stored connections
                        const mockData = {
                            connections: {
                                acumatica: {
                                    isConnected: false, // Set to false to use sample data
                                    instance: 'https://envent-eng.acumatica.com',
                                    credentials: {
                                        username: 'test',
                                        password: 'test'
                                    }
                                }
                            }
                        };
                        callback(mockData);
                    },
                    set: (data, callback) => {
                        console.log('Mock chrome.storage.local.set called with:', data);
                        if (callback) callback();
                    }
                }
            }
        };

        // Mock IndexedDB for testing
        if (!window.indexedDB) {
            console.warn('IndexedDB not available, using mock');
        }
    </script>

    <!-- Load the components -->
    <script type="module">
        // Import the connection manager
        import { connectionManager } from './core/connection.js';
        
        // Import the Price Tracker component
        import { PriceTrackerComponent } from './kpi/purchasing/price_tracker.js';

        // Initialize the connection manager
        await connectionManager.init();

        // Create and initialize the Price Tracker component
        const container = document.getElementById('price-tracker-container');
        const priceTracker = new PriceTrackerComponent(container);
        
        try {
            await priceTracker.init();
            console.log('Price Tracker component initialized successfully');
        } catch (error) {
            console.error('Error initializing Price Tracker component:', error);
            container.innerHTML = `
                <div class="text-red-600 p-4">
                    <h3 class="font-bold">Error initializing Price Tracker:</h3>
                    <p>${error.message}</p>
                </div>
            `;
        }
    </script>
</body>
</html>
