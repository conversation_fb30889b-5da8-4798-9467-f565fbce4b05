// Price Tracker component for KPI Dashboard
import { NotificationSystem } from "../../core/notifications.js";
import { connectionManager } from "../../core/connection.js";

export class PriceTrackerComponent {
  constructor(container) {
    this.container = container;
    this.partHistoryData = [];
    this.filteredData = [];
    this.pivotData = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'inventoryId';
    this.sortDirection = 'asc';
    this.filterStatus = 'all'; // all, stock, nonstock
    this.isLoading = true;
    this.dateRange = {
      start: null,
      end: null,
      preset: '1Y' // 3M, 6M, 1Y, 2Y, Custom
    };
    
    // Database related properties
    this.db = null;
    this.dbName = 'priceTrackerDB';
    this.dbVersion = 1;
    this.storeName = 'partHistory';
    this.settingsStoreName = 'settings';
    this.dbReady = false;
    
    // Notification system
    this.notificationSystem = new NotificationSystem();
    
    // API endpoint
    this.apiEndpoint = 'https://envent-eng.acumatica.com/odata/Envent%20CA%20-%20Live/PartPurchaseHistoryGI';
    
    // Month columns for pivot table
    this.monthColumns = [];
  }

  async init() {
    console.log("Initializing Price Tracker component");
    
    // Render the initial view with loading state
    this.showLoading();
    this.render();
    
    try {
      // Initialize IndexedDB
      await this.initDatabase();
      
      // Set default date range
      this.setDefaultDateRange();
      
      // Load data
      await this.loadPriceData();
      
      // Update loading state and render again
      this.isLoading = false;
      this.hideLoading();
      this.render();
      
      // Set up event listeners
      this.setupEventListeners();
    } catch (error) {
      console.error("Error initializing price tracker:", error);
      this.showError("Failed to initialize: " + error.message);
    }
  }
  
  async initDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = (event) => {
        console.error("IndexedDB error:", event.target.error);
        reject("Could not open price tracker database. Please ensure your browser supports IndexedDB.");
      };
      
      request.onupgradeneeded = (event) => {
        console.log("Creating or upgrading price tracker database");
        const db = event.target.result;
        
        // Create the partHistory object store if it doesn't exist
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: "id" });
          
          // Create indexes for searching and sorting
          store.createIndex("inventoryId", "inventoryId", { unique: false });
          store.createIndex("vendorName", "vendorName", { unique: false });
          store.createIndex("purchaseDate", "purchaseDate", { unique: false });
          store.createIndex("isStock", "isStock", { unique: false });
          
          console.log("Part history object store created");
        }
        
        // Create settings store
        if (!db.objectStoreNames.contains(this.settingsStoreName)) {
          db.createObjectStore(this.settingsStoreName, { keyPath: "id" });
          console.log("Settings object store created");
        }
      };
      
      request.onsuccess = (event) => {
        this.db = event.target.result;
        this.dbReady = true;
        console.log("Price tracker database initialized successfully");
        resolve();
      };
    });
  }

  setDefaultDateRange() {
    const now = new Date();
    const oneYearAgo = new Date(now);
    oneYearAgo.setFullYear(now.getFullYear() - 1);
    
    this.dateRange = {
      start: oneYearAgo,
      end: now,
      preset: '1Y'
    };
    
    this.generateMonthColumns();
  }

  generateMonthColumns() {
    this.monthColumns = [];
    const start = new Date(this.dateRange.start);
    const end = new Date(this.dateRange.end);
    
    // Start from the beginning of the start month
    start.setDate(1);
    start.setHours(0, 0, 0, 0);
    
    const current = new Date(start);
    
    while (current <= end) {
      const monthKey = `${current.getFullYear()}-${String(current.getMonth() + 1).padStart(2, '0')}`;
      const monthLabel = current.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
      
      this.monthColumns.push({
        key: monthKey,
        label: monthLabel,
        date: new Date(current)
      });
      
      // Move to next month
      current.setMonth(current.getMonth() + 1);
    }
  }

  async loadPriceData(forceRefresh = false) {
    try {
      if (!this.dbReady) {
        throw new Error("Database not initialized");
      }
      
      this.isLoading = true;
      this.render();
      
      let data = [];
      
      // If we're not forcing a refresh, try to get data from IndexedDB first
      if (!forceRefresh) {
        data = await this.getDataFromIndexedDB();
      }
      
      // If IndexedDB is empty or forceRefresh is true, fetch from API
      if (data.length === 0 || forceRefresh) {
        // Check if connected to Acumatica
        if (!connectionManager.connections.acumatica.isConnected) {
          console.log("Not connected to Acumatica, using sample data");
          // Use sample data if not connected
          data = this.generateSampleData();
        } else {
          console.log("Connected to Acumatica, fetching price data");
          // Fetch from Acumatica
          const response = await this.fetchAcumaticaPriceData();

          if (response.success) {
            data = this.parseAcumaticaData(response.data);
            // Store in IndexedDB for future use
            await this.storeDataInIndexedDB(data);
          } else {
            console.warn("Failed to fetch from Acumatica, falling back to sample data:", response.error);
            // Fall back to sample data instead of throwing error
            data = this.generateSampleData();
            this.showError("Could not fetch live data: " + response.error + ". Using sample data.");
          }
        }
      }
      
      this.partHistoryData = data;
      this.processDataForPivot();
      this.calculateTotalPages();
      
      this.isLoading = false;
      this.render();
      
      return data;
    } catch (error) {
      console.error("Error loading price data:", error);
      this.showError("Error loading price data: " + error.message);
      this.isLoading = false;
      this.render();
      return [];
    }
  }

  async fetchAcumaticaPriceData() {
    try {
      // Check if connected to Acumatica
      if (!connectionManager.connections.acumatica.isConnected) {
        return { success: false, error: "Not connected to Acumatica. Please connect first." };
      }

      const credentials = connectionManager.connections.acumatica.credentials;
      const instance = connectionManager.connections.acumatica.instance;

      if (!credentials || !instance) {
        return { success: false, error: "Missing Acumatica credentials or instance." };
      }

      // Build query with date filter and pagination
      const startDate = this.dateRange.start.toISOString().split('T')[0];
      const endDate = this.dateRange.end.toISOString().split('T')[0];

      // Use pagination to handle large datasets - fetch in chunks of 1000
      const pageSize = 1000;
      let allData = [];
      let skip = 0;
      let hasMoreData = true;

      console.log("Fetching price data from Acumatica with pagination...");

      // Update loading message to show progress
      this.updateLoadingMessage("Connecting to Acumatica...");

      while (hasMoreData) {
        const currentPage = Math.floor(skip / pageSize) + 1;
        const url = `${this.apiEndpoint}?$filter=Date ge ${startDate} and Date le ${endDate}&$top=${pageSize}&$skip=${skip}`;

        console.log(`Fetching page ${currentPage}, URL:`, url);
        this.updateLoadingMessage(`Fetching page ${currentPage}...`);

        // Create Basic Auth header
        const basicAuth = 'Basic ' + btoa(`${credentials.username}:${credentials.password}`);

        // Make the request with Basic Authentication
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': basicAuth
          }
        });

        if (!response.ok) {
          if (response.status === 401) {
            // Mark as disconnected and return error
            connectionManager.connections.acumatica.isConnected = false;
            if (chrome?.storage?.local) {
              await chrome.storage.local.set({ 'connections': connectionManager.connections });
            }
            return { success: false, error: "Authentication failed. Please reconnect to Acumatica." };
          }
          if (response.status === 429) {
            // Handle throttling - wait longer and retry
            console.warn("Request throttled, waiting 2 seconds before retry...");
            this.updateLoadingMessage("Request throttled, waiting...");
            await new Promise(resolve => setTimeout(resolve, 2000));
            continue; // Retry the same request
          }
          return { success: false, error: `API error: ${response.status} ${response.statusText}` };
        }

        const data = await response.json();
        const pageData = data.value || [];

        if (pageData.length === 0) {
          hasMoreData = false;
        } else {
          allData = allData.concat(pageData);
          skip += pageSize;

          // If we got less than pageSize records, we've reached the end
          if (pageData.length < pageSize) {
            hasMoreData = false;
          }

          // Add a small delay between requests to avoid throttling
          if (hasMoreData) {
            await new Promise(resolve => setTimeout(resolve, 200)); // Increased delay
          }
        }

        const progressMessage = `Fetched ${allData.length} records (page ${currentPage})`;
        console.log(progressMessage);
        this.updateLoadingMessage(progressMessage);
      }

      console.log(`Completed fetching ${allData.length} total price records`);
      return { success: true, data: allData };
    } catch (error) {
      console.error("Error fetching Acumatica price data:", error);
      return { success: false, error: error.message };
    }
  }

  parseAcumaticaData(apiData) {
    try {
      if (!Array.isArray(apiData)) {
        console.error("Price data is not an array:", apiData);
        return [];
      }

      return apiData.map((item, index) => {
        // Parse date
        const purchaseDate = item.Date ? new Date(item.Date) : null;

        // Clean up inventory ID (trim whitespace)
        const inventoryId = (item.InventoryID || '').trim();

        // Parse unit cost
        const unitCost = parseFloat(item.UnitCost || 0);

        return {
          id: `${item.OrderNbr || 'unknown'}-${item.LineNbr || index}`,
          orderNbr: item.OrderNbr || '',
          inventoryId: inventoryId,
          vendorId: item.Vendor || '',
          vendorName: item.Vendor_2 || 'Unknown Vendor',
          purchaseDate: purchaseDate,
          unitCost: unitCost,
          isStock: item.Isstock === true,
          receiptNbr: item.ReceiptNbr || '',
          lineNbr: item.LineNbr || 0,
          type: item.Type || '',
          lastModified: new Date()
        };
      }).filter(item => item.inventoryId && item.purchaseDate); // Filter out invalid entries
    } catch (error) {
      console.error("Error parsing Acumatica price data:", error);
      return [];
    }
  }

  async getDataFromIndexedDB() {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject("Database not initialized");
        return;
      }

      try {
        const transaction = this.db.transaction([this.storeName], "readonly");
        const store = transaction.objectStore(this.storeName);
        const request = store.getAll();

        request.onerror = (event) => {
          console.error("Error getting price data from IndexedDB:", event.target.error);
          reject("Failed to retrieve price data from local storage");
        };

        request.onsuccess = (event) => {
          const data = event.target.result;
          // Parse stored date strings back to Date objects
          data.forEach(item => {
            if (item.purchaseDate && typeof item.purchaseDate === 'string') {
              item.purchaseDate = new Date(item.purchaseDate);
            }
            if (item.lastModified && typeof item.lastModified === 'string') {
              item.lastModified = new Date(item.lastModified);
            }
          });
          console.log(`Retrieved ${data.length} price records from IndexedDB`);
          resolve(data);
        };
      } catch (error) {
        console.error("Error in getDataFromIndexedDB:", error);
        reject(error.message);
      }
    });
  }

  async storeDataInIndexedDB(data) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject("Database not initialized");
        return;
      }

      try {
        const transaction = this.db.transaction([this.storeName], "readwrite");
        const store = transaction.objectStore(this.storeName);

        // Clear existing data
        const clearRequest = store.clear();

        clearRequest.onsuccess = () => {
          console.log("Cleared existing price data");

          // Add new data
          let successCount = 0;
          data.forEach(item => {
            // Convert dates to strings for storage
            const itemToStore = {
              ...item,
              purchaseDate: item.purchaseDate ? item.purchaseDate.toISOString() : null,
              lastModified: item.lastModified ? item.lastModified.toISOString() : null
            };

            const request = store.add(itemToStore);
            request.onsuccess = () => {
              successCount++;
              if (successCount === data.length) {
                console.log(`Successfully stored ${successCount} price records in IndexedDB`);
                resolve();
              }
            };
            request.onerror = (event) => {
              console.error("Error storing price record:", item, event.target.error);
            };
          });

          if (data.length === 0) {
            resolve();
          }
        };

        clearRequest.onerror = (event) => {
          console.error("Error clearing price store:", event.target.error);
          reject("Failed to clear price store");
        };

        transaction.oncomplete = () => {
          console.log("Transaction completed");
        };

        transaction.onerror = (event) => {
          console.error("Transaction error:", event.target.error);
          reject("Transaction failed");
        };
      } catch (error) {
        console.error("Error in storeDataInIndexedDB:", error);
        reject(error.message);
      }
    });
  }

  processDataForPivot() {
    // Group data by InventoryID + Vendor combination
    const groupedData = {};

    this.partHistoryData.forEach(item => {
      const key = `${item.inventoryId}|${item.vendorName}|${item.isStock}`;

      if (!groupedData[key]) {
        groupedData[key] = {
          inventoryId: item.inventoryId,
          vendorName: item.vendorName,
          isStock: item.isStock,
          monthlyPrices: {}
        };
      }

      // Get month key for this purchase
      const monthKey = `${item.purchaseDate.getFullYear()}-${String(item.purchaseDate.getMonth() + 1).padStart(2, '0')}`;

      // Store all purchases for this month (we might have multiple purchases in same month)
      if (!groupedData[key].monthlyPrices[monthKey]) {
        groupedData[key].monthlyPrices[monthKey] = [];
      }

      groupedData[key].monthlyPrices[monthKey].push({
        unitCost: item.unitCost,
        purchaseDate: item.purchaseDate,
        orderNbr: item.orderNbr
      });
    });

    // Convert to array and apply filters
    this.pivotData = Object.values(groupedData);
    this.applyFilters();
  }

  applyFilters() {
    let filtered = [...this.pivotData];

    // Apply search filter
    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(item =>
        item.inventoryId.toLowerCase().includes(searchLower) ||
        item.vendorName.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (this.filterStatus !== 'all') {
      const isStockFilter = this.filterStatus === 'stock';
      filtered = filtered.filter(item => item.isStock === isStockFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (this.sortField) {
        case 'inventoryId':
          aValue = a.inventoryId;
          bValue = b.inventoryId;
          break;
        case 'vendorName':
          aValue = a.vendorName;
          bValue = b.vendorName;
          break;
        case 'isStock':
          aValue = a.isStock ? 'Stock' : 'Non-Stock';
          bValue = b.isStock ? 'Stock' : 'Non-Stock';
          break;
        default:
          aValue = a.inventoryId;
          bValue = b.inventoryId;
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (this.sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    this.filteredData = filtered;
    this.calculateTotalPages();
  }

  calculateTotalPages() {
    this.totalPages = Math.ceil(this.filteredData.length / this.itemsPerPage);
    if (this.currentPage > this.totalPages) {
      this.currentPage = Math.max(1, this.totalPages);
    }
  }

  generateSampleData() {
    const sampleData = [];

    // More realistic inventory IDs and vendors based on the API response
    const inventoryIds = [
      'NC7WZ16P6X', 'FREIGHT', '1100205', '2200305', '3300405',
      'CAP001', 'RES002', 'IC003', 'CONN004', 'WIRE005',
      'SCREW006', 'BOLT007', 'NUT008', 'WASHER009', 'GASKET010'
    ];

    const vendors = [
      { id: 'VENG000298', name: 'Newark' },
      { id: 'VENG000403', name: 'Swagelok Calgary' },
      { id: 'VENG000330', name: 'Phase Tech Industries Ltd' },
      { id: 'VENG000401', name: 'Digi-Key Electronics' },
      { id: 'VENG000402', name: 'Mouser Electronics' },
      { id: 'VENG000405', name: 'McMaster-Carr' },
      { id: 'VENG000406', name: 'Grainger' },
      { id: 'VENG000407', name: 'RS Components' }
    ];

    // Generate sample data for the selected date range
    const startDate = this.dateRange.start;
    const endDate = this.dateRange.end;

    let id = 1;

    // Generate more realistic data with multiple purchases per part/vendor combination
    for (let i = 0; i < 200; i++) {
      const randomDate = new Date(startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime()));
      const randomInventory = inventoryIds[Math.floor(Math.random() * inventoryIds.length)];
      const randomVendor = vendors[Math.floor(Math.random() * vendors.length)];

      // More realistic pricing based on part type
      let baseCost = 1.0;
      if (randomInventory.includes('FREIGHT')) {
        baseCost = 50 + Math.random() * 100; // $50-150 for freight
      } else if (randomInventory.includes('IC') || randomInventory.includes('CAP')) {
        baseCost = 0.1 + Math.random() * 5; // $0.10-5.10 for electronics
      } else if (randomInventory.includes('BOLT') || randomInventory.includes('SCREW')) {
        baseCost = 0.05 + Math.random() * 2; // $0.05-2.05 for hardware
      } else {
        baseCost = 0.1 + Math.random() * 20; // $0.10-20.10 for other parts
      }

      // Add some price variation (±20%)
      const priceVariation = 0.8 + Math.random() * 0.4;
      const finalCost = (baseCost * priceVariation).toFixed(6);

      const randomStock = Math.random() > 0.3; // 70% stock items, 30% non-stock

      sampleData.push({
        id: `SAMPLE-${String(id).padStart(6, '0')}`,
        orderNbr: `02${String(5000 + i).padStart(4, '0')}`,
        inventoryId: randomInventory,
        vendorId: randomVendor.id,
        vendorName: randomVendor.name,
        purchaseDate: randomDate,
        unitCost: parseFloat(finalCost),
        isStock: randomStock,
        receiptNbr: `00${String(6000 + i).padStart(4, '0')}`,
        lineNbr: 1,
        type: 'RN',
        lastModified: new Date()
      });

      id++;
    }

    console.log(`Generated ${sampleData.length} sample price records`);
    return sampleData;
  }

  formatCurrency(amount) {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return '–';
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(amount);
  }

  escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  render() {
    if (!this.container) return;

    this.container.innerHTML = `
      <div class="price-tracker-container">
        ${this.renderHeader()}
        ${this.renderContent()}
      </div>
    `;
  }

  renderHeader() {
    return `
      <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4 md:mb-0">Price Tracker</h2>

        <div class="flex flex-wrap items-center gap-2">
          <!-- Search Input -->
          <div class="relative">
            <input
              type="text"
              id="price-search"
              placeholder="Search parts..."
              class="w-full sm:w-64 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 text-gray-900 dark:text-white"
              value="${this.searchTerm || ''}"
            >
            <button id="clear-search" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Status Filter -->
          <select id="status-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 text-gray-900 dark:text-white">
            <option value="all" ${this.filterStatus === 'all' ? 'selected' : ''}>All Parts</option>
            <option value="stock" ${this.filterStatus === 'stock' ? 'selected' : ''}>Stock Items</option>
            <option value="nonstock" ${this.filterStatus === 'nonstock' ? 'selected' : ''}>Non-Stock Items</option>
          </select>

          <!-- Date Range Button -->
          <button id="date-range-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors" title="Date Range">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
          </button>

          <!-- Refresh Button -->
          <button id="refresh-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors" title="Refresh Data">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          </button>

          <!-- Export Button -->
          <button id="export-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors" title="Export Data">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
            </svg>
          </button>

          <!-- Settings Button -->
          <button id="settings-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors" title="Settings">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
  }

  renderContent() {
    if (this.isLoading) {
      return this.renderLoadingState();
    }

    if (this.filteredData.length === 0) {
      return this.renderEmptyState();
    }

    return `
      <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        ${this.renderPivotTable()}
        ${this.renderPagination()}
      </div>
    `;
  }

  renderLoadingState() {
    return `
      <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
        <div class="flex flex-col items-center justify-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
          <p id="loading-message" class="text-gray-600 dark:text-gray-400">Loading price data...</p>
        </div>
      </div>
    `;
  }

  updateLoadingMessage(message) {
    const loadingElement = this.container?.querySelector('#loading-message');
    if (loadingElement) {
      loadingElement.textContent = message;
    }
  }

  renderEmptyState() {
    return `
      <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No price data found</h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            ${this.searchTerm ? 'No parts match your search criteria.' : 'No price data available for the selected date range.'}
          </p>
          <button id="refresh-data" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
            Refresh Data
          </button>
        </div>
      </div>
    `;
  }

  renderPivotTable() {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageData = this.filteredData.slice(startIndex, endIndex);

    return `
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <!-- Fixed columns -->
              <th class="sticky left-0 z-10 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700" data-sort="inventoryId">
                Inventory ID <span class="sort-indicator">${this.sortField === 'inventoryId' ? (this.sortDirection === 'asc' ? '↑' : '↓') : ''}</span>
              </th>
              <th class="sticky left-0 z-10 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700" data-sort="vendorName" style="left: 200px;">
                Vendor <span class="sort-indicator">${this.sortField === 'vendorName' ? (this.sortDirection === 'asc' ? '↑' : '↓') : ''}</span>
              </th>
              <th class="sticky left-0 z-10 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700" data-sort="isStock" style="left: 400px;">
                Type <span class="sort-indicator">${this.sortField === 'isStock' ? (this.sortDirection === 'asc' ? '↑' : '↓') : ''}</span>
              </th>

              <!-- Dynamic month columns -->
              ${this.monthColumns.map(month => `
                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider min-w-[120px]">
                  ${month.label}
                </th>
              `).join('')}
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            ${pageData.map(row => this.renderTableRow(row)).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  renderTableRow(row) {
    return `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <!-- Fixed columns -->
        <td class="sticky left-0 z-10 px-3 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400 font-medium bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700" style="width: 200px;">
          ${this.escapeHtml(row.inventoryId)}
        </td>
        <td class="sticky left-0 z-10 px-3 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700" style="left: 200px; width: 200px;">
          ${this.escapeHtml(row.vendorName)}
        </td>
        <td class="sticky left-0 z-10 px-3 py-4 whitespace-nowrap text-sm bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700" style="left: 400px; width: 100px;">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${row.isStock ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'}">
            ${row.isStock ? 'Stock' : 'Non-Stock'}
          </span>
        </td>

        <!-- Dynamic month columns -->
        ${this.monthColumns.map(month => {
          const monthData = row.monthlyPrices[month.key];
          return `
            <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-800 dark:text-gray-200 min-w-[120px]">
              ${this.renderMonthCell(monthData)}
            </td>
          `;
        }).join('')}
      </tr>
    `;
  }

  renderMonthCell(monthData) {
    if (!monthData || monthData.length === 0) {
      return '<span class="text-gray-400">–</span>';
    }

    if (monthData.length === 1) {
      // Single purchase in this month
      return `<span class="font-medium">${this.formatCurrency(monthData[0].unitCost)}</span>`;
    } else {
      // Multiple purchases in this month - show latest price with count
      const latest = monthData.reduce((latest, current) =>
        current.purchaseDate > latest.purchaseDate ? current : latest
      );

      return `
        <div class="text-center">
          <span class="font-medium block">${this.formatCurrency(latest.unitCost)}</span>
          <span class="text-xs text-gray-500 dark:text-gray-400">(${monthData.length} purchases)</span>
        </div>
      `;
    }
  }

  renderPagination() {
    if (this.totalPages <= 1) {
      return '';
    }

    return `
      <div class="flex flex-col sm:flex-row justify-between items-center mt-4 px-6 py-3 border-t border-gray-200 dark:border-gray-700 space-y-3 sm:space-y-0">
        <!-- Results Info -->
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredData.length)} to
          ${Math.min(this.currentPage * this.itemsPerPage, this.filteredData.length)} of
          ${this.filteredData.length} results
        </div>

        <!-- Pagination Controls -->
        <div class="flex items-center space-x-1">
          <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-double-left"></i>
          </button>
          <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-left"></i>
          </button>

          <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
            ${this.currentPage} of ${this.totalPages}
          </span>

          <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-right"></i>
          </button>
          <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-double-right"></i>
          </button>
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    // Search functionality with debouncing
    let searchTimeout;
    const searchInput = this.container.querySelector('#price-search');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          this.searchTerm = e.target.value;
          this.currentPage = 1;
          this.applyFilters();
          this.render();
          this.setupEventListeners(); // Re-setup listeners after render
        }, 300); // 300ms debounce
      });
    }

    // Clear search button
    const clearSearchBtn = this.container.querySelector('#clear-search');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        this.searchTerm = '';
        this.currentPage = 1;
        this.applyFilters();
        this.render();
        this.setupEventListeners();
      });
    }

    // Status filter
    const statusFilter = this.container.querySelector('#status-filter');
    if (statusFilter) {
      statusFilter.addEventListener('change', (e) => {
        this.filterStatus = e.target.value;
        this.currentPage = 1;
        this.applyFilters();
        this.render();
        this.setupEventListeners();
      });
    }

    // Date range button
    const dateRangeBtn = this.container.querySelector('#date-range-button');
    if (dateRangeBtn) {
      dateRangeBtn.addEventListener('click', () => {
        this.showDateRangeModal();
      });
    }

    // Refresh button
    const refreshBtn = this.container.querySelector('#refresh-button');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', async () => {
        await this.loadPriceData(true); // Force refresh
        this.setupEventListeners();
      });
    }

    // Export button
    const exportBtn = this.container.querySelector('#export-button');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        this.exportData();
      });
    }

    // Settings button
    const settingsBtn = this.container.querySelector('#settings-button');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => {
        this.showSettingsModal();
      });
    }

    // Table sorting
    const sortHeaders = this.container.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
        this.render();
        this.setupEventListeners();
      });
    });

    // Pagination buttons
    const firstPageBtn = this.container.querySelector('#first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        this.currentPage = 1;
        this.render();
        this.setupEventListeners();
      });
    }

    const prevPageBtn = this.container.querySelector('#prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.render();
          this.setupEventListeners();
        }
      });
    }

    const nextPageBtn = this.container.querySelector('#next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.render();
          this.setupEventListeners();
        }
      });
    }

    const lastPageBtn = this.container.querySelector('#last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        this.currentPage = this.totalPages;
        this.render();
        this.setupEventListeners();
      });
    }

    // Refresh data button (in empty state)
    const refreshDataBtn = this.container.querySelector('#refresh-data');
    if (refreshDataBtn) {
      refreshDataBtn.addEventListener('click', async () => {
        await this.loadPriceData(true);
        this.setupEventListeners();
      });
    }
  }

  showDateRangeModal() {
    // Simple date range selection - could be enhanced with a proper modal
    const presets = [
      { label: '3 Months', value: '3M' },
      { label: '6 Months', value: '6M' },
      { label: '1 Year', value: '1Y' },
      { label: '2 Years', value: '2Y' }
    ];

    const selectedPreset = prompt(
      `Select date range:\n${presets.map((p, i) => `${i + 1}. ${p.label}`).join('\n')}\n\nEnter number (1-4):`,
      presets.findIndex(p => p.value === this.dateRange.preset) + 1
    );

    if (selectedPreset && selectedPreset >= 1 && selectedPreset <= 4) {
      const preset = presets[selectedPreset - 1];
      this.setDateRangeFromPreset(preset.value);
      this.loadPriceData(true);
    }
  }

  setDateRangeFromPreset(preset) {
    const now = new Date();
    let startDate = new Date(now);

    switch (preset) {
      case '3M':
        startDate.setMonth(now.getMonth() - 3);
        break;
      case '6M':
        startDate.setMonth(now.getMonth() - 6);
        break;
      case '1Y':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      case '2Y':
        startDate.setFullYear(now.getFullYear() - 2);
        break;
      default:
        startDate.setFullYear(now.getFullYear() - 1);
        preset = '1Y';
    }

    this.dateRange = {
      start: startDate,
      end: now,
      preset: preset
    };

    this.generateMonthColumns();
  }

  exportData() {
    try {
      // Prepare data for export
      const exportData = [];

      this.filteredData.forEach(row => {
        const baseRow = {
          'Inventory ID': row.inventoryId,
          'Vendor Name': row.vendorName,
          'Type': row.isStock ? 'Stock' : 'Non-Stock'
        };

        // Add month columns
        this.monthColumns.forEach(month => {
          const monthData = row.monthlyPrices[month.key];
          if (monthData && monthData.length > 0) {
            if (monthData.length === 1) {
              baseRow[month.label] = monthData[0].unitCost;
            } else {
              // For multiple purchases, use latest price
              const latest = monthData.reduce((latest, current) =>
                current.purchaseDate > latest.purchaseDate ? current : latest
              );
              baseRow[month.label] = latest.unitCost;
            }
          } else {
            baseRow[month.label] = '';
          }
        });

        exportData.push(baseRow);
      });

      // Convert to CSV
      if (exportData.length === 0) {
        this.showError('No data to export');
        return;
      }

      const headers = Object.keys(exportData[0]);
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header => {
            const value = row[header];
            // Escape commas and quotes in CSV
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          }).join(',')
        )
      ].join('\n');

      // Download CSV
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `price_tracker_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.showSuccess('Data exported successfully');
    } catch (error) {
      console.error('Export error:', error);
      this.showError('Failed to export data: ' + error.message);
    }
  }

  showSettingsModal() {
    // Simple settings - could be enhanced with a proper modal
    const newItemsPerPage = prompt(
      'Items per page (current: ' + this.itemsPerPage + '):',
      this.itemsPerPage
    );

    if (newItemsPerPage && !isNaN(newItemsPerPage) && newItemsPerPage > 0) {
      this.itemsPerPage = parseInt(newItemsPerPage);
      this.currentPage = 1;
      this.calculateTotalPages();
      this.render();
      this.setupEventListeners();
    }
  }

  showLoading() {
    this.isLoading = true;
  }

  hideLoading() {
    this.isLoading = false;
  }

  showError(message) {
    console.error('Price Tracker Error:', message);

    // Try to use notification system if available
    try {
      if (this.notificationSystem && typeof this.notificationSystem.show === 'function') {
        this.notificationSystem.show(message, 'error');
      } else {
        // Fallback to simple notification
        this.showSimpleNotification(message, 'error');
      }
    } catch (error) {
      console.error('Error showing notification:', error);
      // Final fallback
      this.showSimpleNotification(message, 'error');
    }
  }

  showSuccess(message) {
    console.log('Price Tracker Success:', message);

    try {
      if (this.notificationSystem && typeof this.notificationSystem.show === 'function') {
        this.notificationSystem.show(message, 'success');
      } else {
        this.showSimpleNotification(message, 'success');
      }
    } catch (error) {
      console.error('Error showing notification:', error);
      this.showSimpleNotification(message, 'success');
    }
  }

  showSimpleNotification(message, type = 'info') {
    // Create a simple toast notification
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
      type === 'error' ? 'bg-red-500 text-white' :
      type === 'success' ? 'bg-green-500 text-white' :
      'bg-blue-500 text-white'
    }`;
    toast.textContent = message;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 5000);
  }

  // Cleanup method
  destroy() {
    if (this.db) {
      this.db.close();
      this.db = null;
    }

    if (this.container) {
      this.container.innerHTML = '';
    }

    // Clear any timeouts
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }
}
